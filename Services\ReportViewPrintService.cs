using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Markup;
using System.Windows.Shapes;

using DriverManagementSystem.Views;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة طباعة مبسطة لـ ReportView الأصلي
    /// </summary>
    public class ReportViewPrintService
    {
        // أبعاد A4 بوحدة DIP
        private const double A4_WIDTH_DIP = 793.7;   // 21.0 cm
        private const double A4_HEIGHT_DIP = 1122.52; // 29.7 cm
        private const double MARGIN_DIP = 37.8;      // 1.0 cm margins

        /// <summary>
        /// طباعة ReportView الأصلي مباشرة
        /// </summary>
        public static bool PrintReportView(ReportView reportView, string documentTitle = "تقرير الزيارة الميدانية")
        {
            try
            {
                if (reportView == null)
                {
                    MessageBox.Show("لا يوجد تقرير للطباعة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }

                // تشخيص البيانات قبل الطباعة
                System.Diagnostics.Debug.WriteLine("🔍 بدء طباعة ReportView الأصلي...");
                if (reportView.DataContext is DriverManagementSystem.ViewModels.ReportViewModel viewModel)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ ViewModel موجود: {viewModel.GetType().Name}");
                    System.Diagnostics.Debug.WriteLine($"🔍 عدد المشاريع في ReportData: {viewModel.ReportData?.Projects?.Count ?? 0}");

                    if (viewModel.ReportData?.Projects?.Any() == true)
                    {
                        for (int i = 0; i < viewModel.ReportData.Projects.Count; i++)
                        {
                            var project = viewModel.ReportData.Projects[i];
                            System.Diagnostics.Debug.WriteLine($"📋 مشروع {i + 1}: {project.SerialNumber} - {project.ProjectNumber} - {project.ProjectName}");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("⚠️ لا توجد مشاريع في ReportData!");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ DataContext ليس من نوع ReportViewModel: {reportView.DataContext?.GetType().Name ?? "null"}");
                }

                // إنشاء حوار الطباعة
                var printDialog = new PrintDialog();

                // عرض حوار اختيار الطابعة
                if (printDialog.ShowDialog() == true)
                {
                    // إنشاء مستند للطباعة من ReportView الأصلي
                    var printDocument = CreatePrintDocument(reportView);
                    
                    if (printDocument != null)
                    {
                        // طباعة المستند
                        printDialog.PrintDocument(printDocument.DocumentPaginator, documentTitle);
                        
                        MessageBox.Show("تم إرسال التقرير للطباعة بنجاح", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        return true;
                    }
                    else
                    {
                        MessageBox.Show("فشل في إنشاء مستند الطباعة", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return false;
                    }
                }
                
                return false; // المستخدم ألغى الطباعة
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// إنشاء مستند طباعة من ReportView الأصلي مع تقسيم الصفحات عند الفواصل
        /// </summary>
        private static FixedDocument CreatePrintDocument(ReportView reportView)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ إنشاء مستند طباعة مع تقسيم الصفحات عند الفواصل");

                // التأكد من تحديث ReportView قبل الطباعة
                reportView.UpdateLayout();

                // إنشاء مستند الطباعة
                var fixedDocument = new FixedDocument();

                // البحث عن الصفحات في ReportView الأصلي
                var pages = FindPrintPages(reportView);

                System.Diagnostics.Debug.WriteLine($"📄 تم العثور على {pages.Count} صفحة للطباعة");

                // إنشاء صفحة طباعة منفصلة لكل صفحة
                for (int i = 0; i < pages.Count; i++)
                {
                    var page = pages[i];
                    System.Diagnostics.Debug.WriteLine($"🖨️ إنشاء صفحة طباعة {i + 1}");

                    // إنشاء صفحة A4
                    var fixedPage = new FixedPage
                    {
                        Width = A4_WIDTH_DIP,
                        Height = A4_HEIGHT_DIP,
                        Background = Brushes.White
                    };

                    // إنشاء VisualBrush من الصفحة الأصلية
                    var visualBrush = new VisualBrush(page)
                    {
                        Stretch = Stretch.Uniform,
                        AlignmentX = AlignmentX.Center,
                        AlignmentY = AlignmentY.Top
                    };

                    // إنشاء Rectangle لعرض محتوى الصفحة
                    var contentRectangle = new Rectangle
                    {
                        Width = A4_WIDTH_DIP - (MARGIN_DIP * 2),
                        Height = A4_HEIGHT_DIP - (MARGIN_DIP * 2),
                        Fill = visualBrush,
                        Margin = new Thickness(MARGIN_DIP)
                    };

                    // إضافة المحتوى للصفحة
                    fixedPage.Children.Add(contentRectangle);

                    // إنشاء PageContent وإضافته للمستند
                    var pageContent = new PageContent();
                    ((IAddChild)pageContent).AddChild(fixedPage);
                    fixedDocument.Pages.Add(pageContent);
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء مستند طباعة بـ {fixedDocument.Pages.Count} صفحة");
                return fixedDocument;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء مستند الطباعة: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// البحث عن الصفحات في ReportView الأصلي بناءً على الفواصل
        /// </summary>
        private static List<Border> FindPrintPages(ReportView reportView)
        {
            var pages = new List<Border>();

            try
            {
                // البحث عن StackPanel الرئيسي الذي يحتوي على الصفحات
                var mainStackPanel = FindChild<StackPanel>(reportView);
                if (mainStackPanel == null)
                {
                    System.Diagnostics.Debug.WriteLine("❌ لم يتم العثور على StackPanel الرئيسي");
                    return pages;
                }

                // البحث عن جميع Border elements التي تحتوي على PrintPageStyle
                foreach (UIElement child in mainStackPanel.Children)
                {
                    if (child is Border border &&
                        border.Style != null &&
                        border.Style.TargetType == typeof(Border))
                    {
                        // التحقق من أن هذا Border يحتوي على محتوى صفحة وليس فاصل
                        if (border.Height != 30 && border.Background != Brushes.Transparent)
                        {
                            pages.Add(border);
                            System.Diagnostics.Debug.WriteLine($"📄 تم العثور على صفحة: {pages.Count}");
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم العثور على {pages.Count} صفحة إجمالية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث عن الصفحات: {ex.Message}");
            }

            return pages;
        }

        /// <summary>
        /// البحث عن عنصر فرعي من نوع معين
        /// </summary>
        private static T FindChild<T>(DependencyObject parent) where T : DependencyObject
        {
            if (parent == null) return null;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is T result)
                    return result;

                var childOfChild = FindChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }

            return null;
        }
    }
}
