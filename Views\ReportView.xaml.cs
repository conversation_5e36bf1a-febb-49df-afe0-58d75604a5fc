using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    public partial class ReportView : UserControl
    {
        public ReportView()
        {
            InitializeComponent();
            // إنشاء ViewModel فقط إذا لم يكن هناك DataContext محدد مسبقاً
            if (DataContext == null)
            {
                DataContext = new ReportViewModel();
                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء ReportViewModel جديد في ReportView");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"✅ تم استخدام DataContext موجود: {DataContext.GetType().Name}");
            }
        }

        /// <summary>
        /// Constructor مع DataContext محدد مسبقاً
        /// </summary>
        public ReportView(ReportViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
        }

        /// <summary>
        /// طباعة التقرير مباشرة
        /// </summary>
        private void PrintReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ReportViewPrintService.PrintReportView(this, "تقرير الزيارة الميدانية");
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معاينة الطباعة
        /// </summary>
        private void PrintPreviewButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ReportViewPrintService.ShowPrintPreview(this);
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحرير قالب العقد
        /// </summary>
        private void EditContractButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var contractEditorWindow = new ContractEditorWindow();
                contractEditorWindow.ShowDialog();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة تحرير العقد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
